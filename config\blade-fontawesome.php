<?php

return [

    'brands' => [

        'prefix' => 'fab',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'regular' => [

        'prefix' => 'far',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'solid' => [

        'prefix' => 'fas',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    /*
    |-----------------------------------------------------------------
    | Pro Icon Sets
    |-----------------------------------------------------------------
    |
    | The following configuration values are for configuring the
    | icon sets available as part of Font Awesome Pro.
    |
    | If you are not using Font Awesome Pro, this can be removed.
    |
    */

    'duotone' => [

        'prefix' => 'fad',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'light' => [

        'prefix' => 'fal',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'thin' => [

        'prefix' => 'fat',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'sharp-light' => [

        'prefix' => 'fal:sharp',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'sharp-regular' => [

        'prefix' => 'far:sharp',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'sharp-solid' => [

        'prefix' => 'fas:sharp',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'sharp-duotone-solid' => [

        'prefix' => 'fad:sharp',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    'sharp-thin' => [

        'prefix' => 'fat:sharp',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

    /*
    |-----------------------------------------------------------------
    | Pro Icon Kits
    |-----------------------------------------------------------------
    |
    | The following configuration values are for configuring the
    | icon sets available as part of Font Awesome Pro.
    |
    | If you are not using Font Awesome Pro, this can be removed.
    |
    */

    'custom' => [

        'prefix' => 'fak',

        'fallback' => '',

        'class' => '',

        'attributes' => [
            // 'width' => 50,
            // 'height' => 50,
        ],

    ],

];

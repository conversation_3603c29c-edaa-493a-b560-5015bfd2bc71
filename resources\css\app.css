@import "tailwindcss";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans:
        "Instrument Sans", ui-sans-serif, system-ui, sans-serif,
        "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";
    --sidebar-background: hsl(var(--sidebar-background));
    --sidebar-foreground: hsl(var(--sidebar-foreground));
    --sidebar-accent: hsl(var(--sidebar-accent));
    --sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
    --sidebar-border: hsl(var(--sidebar-border));
    --sidebar-ring: hsl(var(--sidebar-ring));
}

@custom-variant dark (&:where(.dark, .dark *));

@layer components {
    .bg-sidebar {
        background-color: hsl(var(--sidebar-background));
    }
    
    .bg-sidebar-accent {
        background-color: hsl(var(--sidebar-accent));
    }

    .text-sidebar-accent-foreground {
        color: hsl(var(--sidebar-accent-foreground));
    }

    .hover\:bg-sidebar-accent:hover {
        background-color: hsl(var(--sidebar-accent));
    }

    .hover\:text-sidebar-accent-foreground:hover {
        color: hsl(var(--sidebar-accent-foreground));
    }
}

@layer base {
    :root {
        --sidebar-background: 210 20% 98%;
        --sidebar-foreground: 215 25% 27%;
        --sidebar-accent: 217 33% 17%;
        --sidebar-accent-foreground: 210 40% 98%;
        --sidebar-border: 214 32% 91%;
        --sidebar-ring: 221 83% 53%;
    }

    .dark {
        --sidebar-background: 217 33% 17%;
        --sidebar-foreground: 210 40% 98%;
        --sidebar-accent: 210 40% 96%;
        --sidebar-accent-foreground: 217 33% 17%;
        --sidebar-border: 215 25% 27%;
        --sidebar-ring: 221 83% 65%;
    }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
}

/* Sidebar width transitions */
.sidebar-transition {
    transition:
        width 0.3s ease,
        transform 0.3s ease,
        margin-left 0.3s ease,
        display 0.3s ease;
}

.content-transition {
    transition:
        margin-left 0.3s ease,
        width 0.3s ease;
}

/* Custom file input */
.custom-file-input::-webkit-file-upload-button {
    visibility: hidden;
}

.custom-file-input::before {
    content: "Select files";
    display: inline-block;
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
    outline: none;
    white-space: nowrap;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.dark .custom-file-input::before {
    background: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
}

.custom-file-input:hover::before {
    border-color: #9ca3af;
}

.custom-file-input:active::before {
    background: #e5e7eb;
}

.dark .custom-file-input:active::before {
    background: #4b5563;
}
